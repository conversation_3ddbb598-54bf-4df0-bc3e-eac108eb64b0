<template>
  <view class="container">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-avatar">
        <image 
          :src="userInfo.avatar || '/static/default-avatar.png'" 
          class="avatar-img"
          mode="aspectFill"
        />
      </view>
      <view class="user-info">
        <text class="username">{{ userInfo.nickname || '点击登录' }}</text>
        <text class="user-desc" v-if="!isLoggedIn">登录后可享受更多功能</text>
        <text class="user-desc" v-else>欢迎使用视频去水印工具</text>
      </view>
      <view class="login-btn" @click="handleLogin" v-if="!isLoggedIn">
        <text class="arrow">→</text>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="showUsageGuide">
          <text class="menu-icon">❓</text>
          <text class="menu-text">使用说明</text>
          <text class="menu-arrow">→</text>
        </view>
        
        <view class="menu-item" @click="showAbout">
          <text class="menu-icon">ℹ️</text>
          <text class="menu-text">关于我们</text>
          <text class="menu-arrow">→</text>
        </view>

        <view class="menu-item" @click="contactService">
          <text class="menu-icon">💬</text>
          <text class="menu-text">联系客服</text>
          <text class="menu-arrow">→</text>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-item" @click="showSettings">
          <text class="menu-icon">⚙️</text>
          <text class="menu-text">设置</text>
          <text class="menu-arrow">→</text>
        </view>

        <view class="menu-item" @click="checkUpdate">
          <text class="menu-icon">⬇️</text>
          <text class="menu-text">检查更新</text>
          <text class="version-text">v1.0.0</text>
          <text class="menu-arrow">→</text>
        </view>

      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section" v-if="isLoggedIn">
      <view class="stats-title">使用统计</view>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-number">{{ stats.totalProcessed }}</text>
          <text class="stats-label">处理次数</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ stats.totalSaved }}</text>
          <text class="stats-label">保存文件</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ stats.daysUsed }}</text>
          <text class="stats-label">使用天数</text>
        </view>
      </view>
    </view>

    <!-- 使用说明弹窗 -->
    <uni-popup ref="usagePopup" type="center">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">使用说明</text>
          <text class="close-btn" @click="$refs.usagePopup.close()">✕</text>
        </view>
        <scroll-view scroll-y class="popup-body">
          <view class="guide-item">
            <text class="guide-title">1. 链接解析</text>
            <text class="guide-desc">复制抖音、快手等平台的分享链接，粘贴到输入框中即可解析</text>
          </view>
          <view class="guide-item">
            <text class="guide-title">2. 本地文件</text>
            <text class="guide-desc">可以选择手机相册中的图片或视频进行处理</text>
          </view>
          <view class="guide-item">
            <text class="guide-title">3. 历史记录</text>
            <text class="guide-desc">开启记录历史功能，可在历史页面查看处理过的内容</text>
          </view>
          <view class="guide-item">
            <text class="guide-title">4. 注意事项</text>
            <text class="guide-desc">本工具仅供学习交流使用，请尊重原创作者版权</text>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <!-- 关于我们弹窗 -->
    <uni-popup ref="aboutPopup" type="center">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">关于我们</text>
          <text class="close-btn" @click="$refs.aboutPopup.close()">✕</text>
        </view>
        <view class="popup-body">
          <view class="about-content">
            <text class="about-title">视频去水印工具</text>
            <text class="about-version">版本：v1.0.0</text>
            <text class="about-desc">
              一款简单易用的视频去水印工具，支持多个主流平台的链接解析和本地文件处理。
              我们致力于为用户提供便捷的服务，同时呼吁大家尊重原创作者的版权。
            </text>
            <text class="about-copyright">© 2024 MarkEraser. All rights reserved.</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isLoggedIn: false,
      userInfo: {
        nickname: '',
        avatar: ''
      },
      stats: {
        totalProcessed: 0,
        totalSaved: 0,
        daysUsed: 0
      }
    }
  },
  
  onShow() {
    this.loadUserInfo()
    this.loadStats()
  },
  
  methods: {
    // 加载用户信息
    loadUserInfo() {
      const userInfo = uni.getStorageSync('user_info')
      if (userInfo) {
        this.isLoggedIn = true
        this.userInfo = userInfo
      }
    },
    
    // 加载统计信息
    loadStats() {
      const history = uni.getStorageSync('watermark_history') || []
      this.stats.totalProcessed = history.length
      this.stats.totalSaved = history.filter(item => item.downloaded).length
      
      // 计算使用天数
      if (history.length > 0) {
        const firstUse = Math.min(...history.map(item => item.timestamp))
        const daysDiff = Math.ceil((Date.now() - firstUse) / (1000 * 60 * 60 * 24))
        this.stats.daysUsed = daysDiff
      }
    },
    
    // 处理登录
    handleLogin() {
      if (this.isLoggedIn) return
      
      uni.showModal({
        title: '登录提示',
        content: '当前为演示版本，登录功能暂未开放',
        showCancel: false
      })
    },
    
    // 显示使用说明
    showUsageGuide() {
      this.$refs.usagePopup.open()
    },
    
    // 显示关于我们
    showAbout() {
      this.$refs.aboutPopup.open()
    },
    
    // 联系客服
    contactService() {
      uni.showActionSheet({
        itemList: ['复制QQ群号', '复制微信号', '发送邮件'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.copyToClipboard('123456789', 'QQ群号')
              break
            case 1:
              this.copyToClipboard('markeraser', '微信号')
              break
            case 2:
              this.copyToClipboard('<EMAIL>', '邮箱地址')
              break
          }
        }
      })
    },
    
    // 复制到剪贴板
    copyToClipboard(text, type) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: `${type}已复制`,
            icon: 'success'
          })
        }
      })
    },
    
    // 显示设置
    showSettings() {
      uni.showModal({
        title: '设置',
        content: '设置功能正在开发中，敬请期待',
        showCancel: false
      })
    },
    
    // 检查更新

    checkUpdate() {
      uni.showLoading({
        title: '检查中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '已是最新版本',
          icon: 'success'
        })
      }, 1500)
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #F5F5F5;
}

.user-section {
  display: flex;
  align-items: center;
  padding: 60rpx 40rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.user-avatar {
  margin-right: 30rpx;
}

.avatar-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: rgba(255, 255, 255, 0.2);
}

.user-info {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 10rpx;
}

.user-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

.login-btn {
  padding: 20rpx;
}

.menu-section {
  padding: 40rpx 20rpx;
}

.menu-group {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 32rpx;
  margin-right: 30rpx;
}

.menu-arrow {
  font-size: 24rpx;
  color: #CCCCCC;
}

.arrow {
  font-size: 24rpx;
  color: #999;
}

.close-btn {
  font-size: 32rpx;
  color: #999;
  padding: 10rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.version-text {
  font-size: 26rpx;
  color: #999;
  margin-right: 20rpx;
}

.stats-section {
  margin: 0 20rpx 40rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 26rpx;
  color: #666;
}

.popup-content {
  width: 600rpx;
  max-height: 800rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.popup-body {
  max-height: 600rpx;
  padding: 40rpx;
}

.guide-item {
  margin-bottom: 40rpx;
}

.guide-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.guide-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.about-content {
  text-align: center;
}

.about-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.about-version {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
}

.about-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  display: block;
  margin-bottom: 40rpx;
}

.about-copyright {
  font-size: 24rpx;
  color: #CCCCCC;
}
</style>
