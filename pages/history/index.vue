<template>
  <view class="container">
    <!-- 顶部操作栏 -->
    <view class="header">
      <text class="title">历史记录</text>
      <view class="clear-btn-container" v-if="historyList.length > 0">
        <button class="clear-all-btn" @click="clearAllHistory">
          <text class="clear-icon">🗑️</text>
          <text class="clear-text">清空记录</text>
        </button>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="historyList.length === 0">
      <text class="empty-icon">🕒</text>
      <text class="empty-text">暂无历史记录</text>
      <text class="empty-tip">去水印页面处理的内容会显示在这里</text>
    </view>

    <!-- 历史记录列表 -->
    <scroll-view scroll-y class="history-list" v-else>
      <view class="history-item" v-for="item in historyList" :key="item.id">
        <view class="item-header">
          <view class="item-info">
            <text class="item-title">{{ item.title || '未知标题' }}</text>
            <text class="item-time">{{ formatTime(item.timestamp) }}</text>
          </view>
          <view class="item-actions">
            <text class="action-btn" @click="reprocess(item)">🔄</text>
            <text class="action-btn delete-btn" @click="deleteItem(item.id)">🗑️</text>
          </view>
        </view>

        <view class="item-content">
          <text class="item-author" v-if="item.author">作者：{{ item.author }}</text>
          <text class="item-source" v-if="item.source">来源：{{ item.source }}</text>
        </view>

        <view class="item-footer">
          <button 
            class="download-btn" 
            size="mini" 
            @click="copyDownloadLink(item)"
            v-if="item.downloadUrl"
          >
            复制下载链接
          </button>
          <view class="item-type">
            <text class="type-tag" :class="getTypeClass(item.type)">
              {{ getTypeText(item.type) }}
            </text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部统计信息 -->
    <view class="footer" v-if="historyList.length > 0">
      <text class="stats">共 {{ historyList.length }} 条记录</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      historyList: []
    }
  },
  
  onShow() {
    this.loadHistory()
  },
  
  methods: {
    // 加载历史记录
    loadHistory() {
      const history = uni.getStorageSync('watermark_history') || []
      this.historyList = history.sort((a, b) => b.timestamp - a.timestamp)
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 24小时内
        return Math.floor(diff / 3600000) + '小时前'
      } else if (diff < 604800000) { // 7天内
        return Math.floor(diff / 86400000) + '天前'
      } else {
        return date.toLocaleDateString()
      }
    },
    
    // 获取类型样式类
    getTypeClass(type) {
      switch (type) {
        case 'douyin':
          return 'type-douyin'
        case 'kuaishou':
          return 'type-kuaishou'
        case 'xiaohongshu':
          return 'type-xiaohongshu'
        case 'local':
          return 'type-local'
        default:
          return 'type-default'
      }
    },
    
    // 获取类型文本
    getTypeText(type) {
      switch (type) {
        case 'douyin':
          return '抖音'
        case 'kuaishou':
          return '快手'
        case 'xiaohongshu':
          return '小红书'
        case 'local':
          return '本地文件'
        default:
          return '未知'
      }
    },
    
    // 重新处理
    reprocess(item) {
      // 跳转到主页面并传递数据
      uni.switchTab({
        url: '/pages/watermark-remover/index'
      })
      
      // 通过事件总线传递数据
      uni.$emit('reprocessItem', item)
    },
    
    // 复制下载链接
    copyDownloadLink(item) {
      if (!item.downloadUrl) {
        uni.showToast({
          title: '暂无下载链接',
          icon: 'none'
        })
        return
      }
      
      uni.setClipboardData({
        data: item.downloadUrl,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },
    
    // 删除单个记录
    deleteItem(id) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条记录吗？',
        success: (res) => {
          if (res.confirm) {
            const history = uni.getStorageSync('watermark_history') || []
            const newHistory = history.filter(item => item.id !== id)
            uni.setStorageSync('watermark_history', newHistory)
            this.loadHistory()
            
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 清空所有历史记录
    clearAllHistory() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有历史记录吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            uni.removeStorageSync('watermark_history')
            this.historyList = []
            
            uni.showToast({
              title: '清空成功',
              icon: 'success'
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #F5F5F5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #E5E5E5;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.clear-btn-container {
  display: flex;
  align-items: center;
}

.clear-all-btn {
  display: flex;
  align-items: center;
  background: rgba(255, 59, 48, 0.1);
  border: none;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.clear-all-btn::after {
  border: none;
}

.clear-all-btn:active {
  transform: scale(0.95);
  background: rgba(255, 59, 48, 0.2);
}

.clear-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.clear-text {
  font-size: 26rpx;
  color: #FF3B30;
  font-weight: 500;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin: 40rpx 0 20rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #CCCCCC;
  text-align: center;
}

.history-list {
  flex: 1;
  padding: 20rpx;
}

.history-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.item-time {
  font-size: 24rpx;
  color: #999;
}

.item-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  font-size: 32rpx;
  padding: 10rpx;
  margin-left: 20rpx;
}

.delete-btn {
  color: #FF3B30;
}

.item-content {
  margin-bottom: 20rpx;
}

.item-author,
.item-source {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.download-btn {
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.item-type {
  display: flex;
  align-items: center;
}

.type-tag {
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  color: #ffffff;
}

.type-douyin {
  background: #FF0050;
}

.type-kuaishou {
  background: #FF6600;
}

.type-xiaohongshu {
  background: #FF2442;
}

.type-local {
  background: #34C759;
}

.type-default {
  background: #999999;
}

.footer {
  padding: 30rpx 40rpx;
  background: #ffffff;
  border-top: 1rpx solid #E5E5E5;
  text-align: center;
}

.stats {
  font-size: 26rpx;
  color: #999;
}
</style>
