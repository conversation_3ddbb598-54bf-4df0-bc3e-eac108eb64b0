{
  "pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/watermark-remover/index",
      "style": {
        "navigationBarTitleText": "去水印",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/history/index",
      "style": {
        "navigationBarTitleText": "历史记录",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/profile/index",
      "style": {
        "navigationBarTitleText": "我的",
        "enablePullDownRefresh": false
      }
    },


    {
      "path": "pages/result/index",
      "style": {
        "navigationBarTitleText": "处理结果",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/cloudFunction/cloudFunction",
      "style": {
        "navigationBarTitleText": "云函数",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/cloudObject/cloudObject",
      "style": {
        "navigationBarTitleText": "云对象",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/secure-network/cloud-function",
      "style": {
        "navigationBarTitleText": "安全网络 - 云函数",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/secure-network/cloud-object",
      "style": {
        "navigationBarTitleText": "安全网络 - 云对象",
        "enablePullDownRefresh": false
      }
    },

    {
      "path": "pages/clientDB/unicloud-db-demo/unicloud-db-demo",
      "style": {
        "navigationBarTitleText": "unicloud-db demo",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/clientDB/demo/demo",
      "style": {
        "navigationBarTitleText": "留言板",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/clientDB/permission-table-simple/permission-table-simple",
      "style": {
        "navigationBarTitleText": "表级-简单权限控制",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/clientDB/permission-table-compound/permission-table-compound",
      "style": {
        "navigationBarTitleText": "表级-复杂权限控制",
        "enablePullDownRefresh": false
      }

    },
    {
      "path": "pages/clientDB/clientDB",
      "style": {
        "navigationBarTitleText": "前端操作数据库",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/user-info/add",
      "style": {
        "navigationBarTitleText": "新增"
      }
    }, {
      "path": "pages/user-info/edit",
      "style": {
        "navigationBarTitleText": "修改"
      }
    }, {
      "path": "pages/user-info/list",
      "style": {
        "navigationBarTitleText": "列表"
      }
    }, {
      "path": "pages/user-info/detail",
      "style": {
        "navigationBarTitleText": "详情"
      }
    },
    {
      "path": "pages/storage/storage",
      "style": {
        "navigationBarTitleText": "云存储",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/storage/space-storage",
      "style": {
        "navigationBarTitleText": "空间内置云存储",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/storage/ext-storage-qiniu",
      "style": {
        "navigationBarTitleText": "扩展存储-七牛云",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/schema2code/schema2code",
      "style": {
        "navigationBarTitleText": "schema2code",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/clientDB/permission/permission",
      "style": {
        "navigationBarTitleText": "角色权限",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/clientDB/permission-demo/permission-demo",
      "style": {
        "navigationBarTitleText": "角色权限",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/clientDB/permission-demo/readme",
      "style": {
        "navigationBarTitleText": "角色权限",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/clientDB/permission-field-simple/permission-field-simple",
      "style": {
        "navigationBarTitleText": "简单-字段级权限控制",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/clientDB/clientDB-api/clientDB-api",
      "style": {
        "navigationBarTitleText": "前端操作数据库的API",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/clientDB/validate/validate",
      "style": {
        "navigationBarTitleText": "字段值域验证",
        "enablePullDownRefresh": false
      }

    }, {
      "path": "pages/validate-demo/add",
      "style": {
        "navigationBarTitleText": "新增"
      }
    }, {
      "path": "pages/validate-demo/edit",
      "style": {
        "navigationBarTitleText": "修改"
      }
    }, {
      "path": "pages/validate-demo/list",
      "style": {
        "navigationBarTitleText": ""
      }
    }, {
      "path": "pages/validate-demo/detail",
      "style": {}
    }, {
      "path": "pages/webview/webview",
      "style": {
        "navigationBarTitleText": "",
        "enablePullDownRefresh": false
      }

    },
    {
      "path": "uni_modules/uni-upgrade-center-app/pages/upgrade-popup",
      "style": {
        "disableScroll": true,
        "app-plus": {
          "backgroundColorTop": "transparent",
          "background": "transparent",
          "titleNView": false,
          "scrollIndicator": false,
          "popGesture": "none",
          "animationType": "fade-in",
          "animationDuration": 200

        }
      }
    }, {
      "path": "pages/cloudFunction/redis/redis",
      "style": {
        "navigationBarTitleText": "扩展能力Redis",
        "enablePullDownRefresh": false
      }
    }, {
      "path": "uni_modules/uni-upgrade-center/pages/version/list",
      "style": {
        "navigationBarTitleText": "版本列表"
      }
    }, {
      "path": "uni_modules/uni-upgrade-center/pages/version/add",
      "style": {
        "navigationBarTitleText": "新版发布"
      }
    }, {
      "path": "uni_modules/uni-upgrade-center/pages/version/detail",
      "style": {
        "navigationBarTitleText": "版本信息查看"
      }
    }
],
  "tabBar": {
    "color": "#666666",
    "selectedColor": "#007AFF",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [{
        "pagePath": "pages/watermark-remover/index",
        "iconPath": "static/tabbar/fn.png",
        "selectedIconPath": "static/tabbar/fn1.png",
        "text": "去水印"
      },
      {
        "pagePath": "pages/history/index",
        "iconPath": "static/tabbar/storage.png",
        "selectedIconPath": "static/tabbar/storage1.png",
        "text": "历史"
      },
      {
        "pagePath": "pages/profile/index",
        "iconPath": "static/tabbar/yun.png",
        "selectedIconPath": "static/tabbar/yun1.png",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  }
}
