{"name": "uni-admin", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"version": "1.0.0", "license": "ISC", "dependencies": {"uni-captcha": "file:../../../uni_modules/uni-captcha/uniCloud/cloudfunctions/common/uni-captcha", "uni-cloud-router": "file:../../../uni_modules/uni-cloud-router/uniCloud/cloudfunctions/common/uni-cloud-router", "uni-config-center": "file:../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center", "uni-id": "file:../../../uni_modules/uni-id/uniCloud/cloudfunctions/common/uni-id"}}, "../../../uni_modules/uni-captcha/uniCloud/cloudfunctions/common/uni-captcha": {"version": "0.1.1", "license": "Apache-2.0"}, "../../../uni_modules/uni-cloud-router/uniCloud/cloudfunctions/common/uni-cloud-router": {"version": "0.0.10", "license": "Apache-2.0"}, "../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center": {"version": "0.0.1", "license": "Apache-2.0"}, "../../../uni_modules/uni-id/uniCloud/cloudfunctions/common/uni-id": {"version": "3.0.12", "license": "Apache-2.0", "dependencies": {"uni-config-center": "file:../../../../../uni-config-center/uniCloud/cloudfunctions/common/uni-config-center"}}, "node_modules/uni-captcha": {"resolved": "../../../uni_modules/uni-captcha/uniCloud/cloudfunctions/common/uni-captcha", "link": true}, "node_modules/uni-cloud-router": {"resolved": "../../../uni_modules/uni-cloud-router/uniCloud/cloudfunctions/common/uni-cloud-router", "link": true}, "node_modules/uni-config-center": {"resolved": "../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center", "link": true}, "node_modules/uni-id": {"resolved": "../../../uni_modules/uni-id/uniCloud/cloudfunctions/common/uni-id", "link": true}}, "dependencies": {"uni-captcha": {"version": "file:../../../uni_modules/uni-captcha/uniCloud/cloudfunctions/common/uni-captcha"}, "uni-cloud-router": {"version": "file:../../../uni_modules/uni-cloud-router/uniCloud/cloudfunctions/common/uni-cloud-router"}, "uni-config-center": {"version": "file:../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center"}, "uni-id": {"version": "file:../../../uni_modules/uni-id/uniCloud/cloudfunctions/common/uni-id", "requires": {"uni-config-center": "file:../../../../../uni-config-center/uniCloud/cloudfunctions/common/uni-config-center"}}}}