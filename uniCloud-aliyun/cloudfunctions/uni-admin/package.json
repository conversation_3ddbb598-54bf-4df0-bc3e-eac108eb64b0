{"name": "uni-admin", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"uni-captcha": "file:../../../uni_modules/uni-captcha/uniCloud/cloudfunctions/common/uni-captcha", "uni-cloud-router": "file:../../../uni_modules/uni-cloud-router/uniCloud/cloudfunctions/common/uni-cloud-router", "uni-config-center": "file:../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center", "uni-id": "file:../../../uni_modules/uni-id/uniCloud/cloudfunctions/common/uni-id"}, "origin-plugin-dev-name": "uni-cloud", "origin-plugin-version": "1.4.2", "plugin-dev-name": "uni-cloud", "plugin-version": "1.4.2"}