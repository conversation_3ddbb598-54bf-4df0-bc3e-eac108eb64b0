# B站视频解析器

## 功能概述

B站视频解析器是 MarkEraser 项目的一个云函数模块，专门用于解析B站视频链接并提取无水印视频地址。

## 支持的链接格式

- `https://www.bilibili.com/video/BVxxxxxx` - 标准B站视频链接
- `https://b23.tv/xxxxxx` - B站短链接
- `https://m.bilibili.com/video/BVxxxxxx` - 移动端B站链接

## 主要特性

### 1. 智能解析
- 自动识别B站视频链接
- 支持短链接自动跳转
- 提取视频基本信息（标题、作者、时长等）

### 2. 高质量视频
- 自动选择最高质量的视频流
- 支持DASH格式和传统格式
- 获取无水印直链

### 3. 多端兼容
- 优先使用PC端请求（数据更完整）
- 移动端作为备用方案
- 自动重试机制

### 4. 错误处理
- 完善的错误处理机制
- 详细的错误信息提示
- 网络请求超时保护

## 技术实现

### 解析流程

1. **链接验证** - 检测是否为有效的B站链接
2. **URL处理** - 处理短链接重定向，获取真实URL
3. **页面获取** - 获取视频页面HTML内容
4. **数据提取** - 从页面中提取JSON数据
5. **信息解析** - 解析视频标题、作者、封面等信息
6. **视频URL获取** - 提取最高质量的视频播放地址
7. **结果返回** - 返回标准化的解析结果

### 关键技术

#### 1. JSON数据提取
```javascript
const patterns = [
  /window\.__INITIAL_STATE__\s*=\s*({.+?})<\/script>/s,
  /window\.__playinfo__\s*=\s*({.+?})<\/script>/s,
  /window\.__INITIAL_STATE__\s*=\s*({.+?});/s
];
```

#### 2. 视频质量选择
```javascript
// 优先使用dash格式（更高质量）
if (playInfo.dash && playInfo.dash.video && playInfo.dash.video.length > 0) {
  // 按带宽排序，选择最高质量
  const sortedVideos = playInfo.dash.video.sort((a, b) => {
    return (b.bandwidth || 0) - (a.bandwidth || 0);
  });
}
```

#### 3. 请求头配置
```javascript
const BILIBILI_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...',
  'Referer': 'https://www.bilibili.com/',
  // ... 其他头信息
};
```

## 使用方法

### 1. 直接调用云函数

```javascript
const result = await uniCloud.callFunction({
  name: 'bilibili-parser',
  data: {
    link: 'https://www.bilibili.com/video/BV1xx411c7mu',
    forceRemoveWatermark: false,
    debug: false
  }
});
```

### 2. 通过统一解析器调用

```javascript
const result = await uniCloud.callFunction({
  name: 'unified-parser',
  data: {
    link: 'https://www.bilibili.com/video/BV1xx411c7mu',
    options: {
      forceRemoveWatermark: false,
      debug: false
    }
  }
});
```

### 3. 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| link | string | 是 | B站视频链接 |
| forceRemoveWatermark | boolean | 否 | 是否强制去水印（B站视频通常无需去水印） |
| debug | boolean | 否 | 是否开启调试模式 |
| getCover | boolean | 否 | 是否只获取封面信息 |

### 4. 返回格式

```javascript
{
  success: true,
  data: {
    title: "视频标题",
    author: "UP主名称",
    processedData: {
      data: "视频直链URL",
      type: "video/mp4",
      isUrl: true,
      duration: 180, // 秒
      isLongVideo: false,
      isDirectUrl: true
    },
    type: "video",
    platform: "bilibili",
    source: "B站",
    note: "已获取B站视频直链",
    coverUrl: "封面图片URL",
    originalUrl: "原始链接"
  }
}
```

## 测试

### 测试页面
项目提供了专门的测试页面：`pages/test/bilibili-test.vue`

### 测试步骤
1. 在HBuilderX中打开项目
2. 上传B站解析器云函数
3. 访问测试页面
4. 输入B站视频链接进行测试

### 测试用例
- 标准视频链接：`https://www.bilibili.com/video/BV1xx411c7mu`
- 短链接：`https://b23.tv/xxxxxx`
- 移动端链接：`https://m.bilibili.com/video/BV1xx411c7mu`

## 注意事项

### 1. 使用限制
- 仅支持公开的B站视频
- 不支持会员专享内容
- 不支持已下架或删除的视频

### 2. 网络要求
- 需要稳定的网络连接
- 建议在服务器端使用
- 客户端可能受到跨域限制

### 3. 合规使用
- 请遵守B站用户协议
- 仅用于个人学习和研究
- 不得用于商业用途

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持B站视频链接解析
- 支持短链接自动跳转
- 支持高质量视频提取
- 集成到统一解析器架构

## 技术支持

如有问题或建议，请：
1. 查看项目文档
2. 检查云函数日志
3. 联系开发团队

---

**注意：本解析器仅供学习和研究使用，请遵守相关平台的使用条款。**


