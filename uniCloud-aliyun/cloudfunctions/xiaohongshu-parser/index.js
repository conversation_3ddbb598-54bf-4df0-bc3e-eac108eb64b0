'use strict';

/**
 * 小红书内容解析器
 * 支持小红书视频和图文内容的解析，包括去水印处理
 */


exports.main = async (event, context) => {

  const { link, forceRemoveWatermark = false } = event;

  if (!link) {
    return {
      success: false,
      message: '链接不能为空'
    };
  }

  try {
    // 检测是否为小红书链接
    if (!isXiaohongshuLink(link)) {
      return {
        success: false,
        message: '仅支持小红书链接'
      };
    }


    // 解析小红书内容
    const result = await parseXiaohongshuContent(link);

    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      version: "小红书解析器"
    };

  } catch (error) {

    return {
      success: false,
      message: error.message || '解析失败',
      error: undefined
    };
  }
};

// 检测是否为小红书链接
function isXiaohongshuLink(url) {
  const patterns = [
    /xiaohongshu\.com/i,
    /xhslink\.com/i,
    /xhs\.link/i
  ];
  return patterns.some(pattern => pattern.test(url));
}


// 从页面内容中提取图片和视频URL（优先使用无水印方法，保持Live Photo对应关系）
async function extractMediaUrls(html) {
  try {
    // 优先方法：从 __INITIAL_STATE__ 提取无水印内容
    const noWatermarkData = extractNoWatermarkFromInitialState(html);
    let imageUrls = noWatermarkData.imageUrls.filter(url => url !== null);

    let livePhotoVideos = noWatermarkData.livePhotoVideos;
    let coverUrl = noWatermarkData.coverUrl;
    
    // 处理主视频（非Live Photo）
    let videoUrls = noWatermarkData.videoUrl ? [noWatermarkData.videoUrl] : [];
    
    // 将Live Photo视频添加到视频列表（保持对应关系）
    if (livePhotoVideos && livePhotoVideos.length > 0) {
      // 只添加非空的Live Photo视频
      const validLivePhotos = livePhotoVideos.filter(url => url !== null);
      videoUrls.push(...validLivePhotos);
    }

  
    // Live Photo视频不需要去重，保持与图片的一一对应关系
    videoUrls = videoUrls.filter(url => url && isValidMediaUrl(url, 'video'));

    const result = { imageUrls, videoUrls, livePhotoVideos, coverUrl };
    // 临时调试：在note字段中返回调试信息
    result.debugInfo = `图片:${imageUrls.length}个,视频:${videoUrls.length}个,LivePhoto:${livePhotoVideos.length}个`;
    return result;

  } catch (error) {
    return { imageUrls: [], videoUrls: [], livePhotoVideos: [], coverUrl: null };
  }
}



// 验证URL是否有效
function isValidMediaUrl(url, type) {
  if (!url || typeof url !== 'string') return false;
  
  // 过滤掉界面元素和推荐内容
  const excludePatterns = [
    'avatar', 'icon', 'logo', 'thumb', 'cover', 'recommend', 'related',
    'feed', 'list', 'static.xiaohongshu.com', 'picasso-static', 'fe-platform'
  ];
  
  if (excludePatterns.some(pattern => url.includes(pattern))) {
    return false;
  }

  // 过滤小尺寸缩略图
  if (url.match(/[wh]_\d{1,3}[^0-9]/)) {
    return false;
  }

  // 类型特定验证
  if (type === 'image') {
    return url.includes('xhscdn') && (
      url.includes('imageView') || 
      /\.(jpg|jpeg|png|webp)/i.test(url) ||
      url.includes('nd_prv_wlteh_webp') ||
      url.includes('nd_dft_wlteh_webp') ||
      url.includes('sns-webpic')
    );
  } else if (type === 'video') {
    return url.includes('.mp4') || url.includes('sns-video');
  }

  return true;
}

// 从 __INITIAL_STATE__ 中提取无水印内容
function extractNoWatermarkFromInitialState(html) {
  try {
    // 匹配 window.__INITIAL_STATE__ 数据
    const initialStateMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({[\s\S]*?})<\/script>/);
    if (!initialStateMatch) {
      return { videoUrl: null, imageUrls: [], livePhotoVideos: [] };
    }

    let jsonData = initialStateMatch[1];
    // 将 undefined 替换为 null（参考PHP版本处理方式）
    jsonData = jsonData.replace(/undefined/g, 'null');

    try {
      const decoded = JSON.parse(jsonData);
      
      let noteData = null;
      
      // PC端数据结构路径：note -> noteDetailMap -> firstNoteId -> note
      const noteRoot = decoded?.note;
      if (noteRoot && noteRoot.firstNoteId && noteRoot.noteDetailMap) {
        const firstNoteId = noteRoot.firstNoteId;
        const noteDetailMap = noteRoot.noteDetailMap;
        if (noteDetailMap[firstNoteId] && noteDetailMap[firstNoteId].note) {
          noteData = noteDetailMap[firstNoteId].note;
        }
      }
      
      if (!noteData) {
        return { videoUrl: null, imageUrls: [], livePhotoVideos: [] };
      }

      let videoUrl = null;
      let imageUrls = [];
      let livePhotoVideos = [];

      // 提取主视频URL（如果有的话）
      const videoData = noteData?.video?.media?.stream?.h264?.[0]?.masterUrl || 
                       noteData?.video?.media?.stream?.h265?.[0]?.masterUrl ||
                       noteData?.video?.media?.masterUrl;
      
      
      if (videoData && typeof videoData === 'string') {
        videoUrl = videoData;
        console.log('提取到主视频URL:', videoUrl);
      }

      // 提取图片URLs和对应的Live Photo视频
      const imageList = noteData?.imageList;
      
      if (imageList && Array.isArray(imageList)) {
        for (let i = 0; i < imageList.length; i++) {
          const imageItem = imageList[i];
          
          // 尝试多种图片URL获取方式
          let imageUrl = null;
          
          // 方式1：直接字段（优先使用urlDefault高清版本）
          imageUrl = imageItem?.urlDefault || imageItem?.urlPre || imageItem?.url;
          
          // 方式2：从infoList中获取（优先选择默认版本）
          if (!imageUrl && imageItem?.infoList && Array.isArray(imageItem.infoList)) {
            let defaultUrl = null;
            let previewUrl = null;
            
            for (const info of imageItem.infoList) {
              if (info?.url && info.url.includes('nd_dft_wlteh_webp')) {
                defaultUrl = info.url;
              } else if (info?.url && info.url.includes('nd_prv_wlteh_webp')) {
                previewUrl = info.url;
              }
            }
            
            imageUrl = defaultUrl || previewUrl;
          }
          
          // 调试信息放到返回结果中
          const debugInfo = {
            urlPre: imageItem?.urlPre,
            urlDefault: imageItem?.urlDefault,
            url: imageItem?.url,
            infoListCount: imageItem?.infoList?.length || 0,
            selectedUrl: imageUrl
          };
          
          if (imageUrl && typeof imageUrl === 'string' && imageUrl !== '') {
            imageUrls.push(imageUrl);
          } else {
            imageUrls.push(null);
          }
          
          // 将调试信息存储起来，稍后返回
          if (!global.imageDebugInfo) global.imageDebugInfo = [];
          global.imageDebugInfo.push(`图片${i}:${imageUrl ? '✓' : '✗'} ${JSON.stringify(debugInfo)}`);
          
          // 提取对应的Live Photo视频URL
          let livePhotoUrl = null;
          
          // 检查 livePhoto 标志位
          if (imageItem?.livePhoto === true) {
            // 从stream对象中提取视频URL
            const stream = imageItem?.stream;
            if (stream && typeof stream === 'object') {
              // 按优先级尝试不同格式：h264 > h265 > av1
              const h264Url = stream?.h264?.[0]?.masterUrl;
              const h265Url = stream?.h265?.[0]?.masterUrl;
              const av1Url = stream?.av1?.[0]?.masterUrl;
              
              livePhotoUrl = h264Url || h265Url || av1Url;
              
              if (livePhotoUrl) {
                console.log(`Live Photo ${i} 视频URL:`, livePhotoUrl);
              }
            }
          }
          
          livePhotoVideos.push(livePhotoUrl); // null表示该图片没有Live Photo
        }
      }

      // 封面URL获取逻辑（直接使用第一张图片）
      let coverUrl = null;
      
      // 使用第一张图片作为封面（imageUrls已经优先使用urlDefault）
      if (imageUrls.length > 0) {
        coverUrl = imageUrls[0];
      }


      return { videoUrl, imageUrls, livePhotoVideos, coverUrl };

    } catch (parseError) {
      console.error('解析INITIAL_STATE JSON失败:', parseError);
    }

    return { videoUrl: null, imageUrls: [], livePhotoVideos: [] };
  } catch (error) {
    console.error('提取无水印内容失败:', error);
    return { videoUrl: null, imageUrls: [], livePhotoVideos: [] };
  }
}

// 解析小红书内容
async function parseXiaohongshuContent(shareUrl) {
  try {
    // 清空全局调试信息
    global.imageDebugInfo = [];
    // 获取真实URL和页面内容
    const realUrl = await getRealUrl(shareUrl);
    const contentType = extractContentType(realUrl);
    const pageContent = await getPageContent(realUrl);
    const basicInfo = await extractBasicInfo(pageContent);

    // 统一提取媒体内容
    const mediaResult = await extractMediaUrls(pageContent);
    let imageUrls = mediaResult.imageUrls;
    let videoUrls = mediaResult.videoUrls;
    let livePhotoVideos = mediaResult.livePhotoVideos || [];
    let videoUrl = videoUrls.length > 0 ? videoUrls[0] : null;


    // URL处理
    if (videoUrl && videoUrl !== realUrl) {
      // 检查是否为有效的视频URL
      if (videoUrl.includes('imageView') || videoUrl.includes('format/jpg') || videoUrl.includes('format/png')) {
        videoUrl = realUrl;
      }
    }

    // 如果是视频内容但未找到视频URL，使用页面URL作为备用
    if (contentType === 'video' && !videoUrl) {
      videoUrl = realUrl;
    }

    // 构建内容信息
    let contentInfo = {
      title: basicInfo.title,
      author: basicInfo.author,
      content: basicInfo.content || '', // 添加正文内容
      videoUrl: videoUrl,
      videoUrls: videoUrls, // 所有视频URL
      imageUrls: imageUrls, // 所有图片URL
      livePhotoVideos: livePhotoVideos, // Live Photo视频数组
      isDirectUrl: videoUrl === realUrl, // 标记是否为直接URL
      contentType: contentType
    };

    // 构建返回结果
    let processedData, resultType;

    // 判断主要内容类型
    const hasImages = imageUrls.length > 0;
    const hasVideos = videoUrls.length > 0;

    if (contentType === 'image' || (hasImages && !hasVideos)) {
      // 纯图片内容
      processedData = {
        data: imageUrls[0],
        type: 'image/jpeg',
        isUrl: true,
        imageUrls: imageUrls,
        videoUrls: videoUrls,
        livePhotoVideos: livePhotoVideos, // 添加Live Photo对应关系
        isImageContent: true,
        hasLivePhoto: videoUrls.length > 0
      };
      resultType = 'image';

    } else if (contentType === 'video' || (hasVideos && !hasImages)) {
      // 纯视频内容
      const isDirectVideo = videoUrl && (videoUrl.includes('.mp4') || videoUrl.includes('.m3u8'));

      processedData = {
        data: videoUrl,
        type: isDirectVideo ? 'video/mp4' : 'text/html',
        isUrl: true,
        isDirectUrl: contentInfo.isDirectUrl || false,
        videoUrls: videoUrls,
        imageUrls: imageUrls,
        livePhotoVideos: livePhotoVideos
      };
      resultType = 'video';

    } else if (hasImages && hasVideos) {
      // 混合内容（Live Photo等）
      processedData = {
        data: imageUrls[0],
        type: 'image/jpeg',
        isUrl: true,
        imageUrls: imageUrls,
        videoUrls: videoUrls,
        livePhotoVideos: livePhotoVideos, // 添加Live Photo对应关系
        isImageContent: true,
        hasLivePhoto: true,
        isMixedContent: true
      };
      resultType = 'image';

    } else {
      // 没有找到内容
      processedData = {
        data: null,
        type: 'unknown',
        isUrl: false
      };
      resultType = 'unknown';

    }

    // 获取封面URL
    let coverUrl = mediaResult.coverUrl;

    const note = `解析完成 - ${mediaResult.debugInfo || '无调试信息'}`;

    const result = {
      title: contentInfo.title,
      author: contentInfo.author,
      content: contentInfo.content || '',
      processedData: processedData,
      type: resultType,
      platform: 'xiaohongshu',
      source: '小红书',
      note: note,
      coverUrl: coverUrl,
      originalUrl: shareUrl,
      // 调试信息
      debug: {
        extractedImageUrls: contentInfo.imageUrls || [],
        extractedVideoUrls: contentInfo.videoUrls || [],
        livePhotoVideos: contentInfo.livePhotoVideos || [],
        hasImages: (contentInfo.imageUrls || []).length > 0,
        hasVideos: (contentInfo.videoUrls || []).length > 0,
        contentType: contentType,
        imageDebugInfo: global.imageDebugInfo || [],
        basicInfo: {
          title: contentInfo.title,
          author: contentInfo.author,
          content: contentInfo.content
        }
      }
    };


    return result;

  } catch (error) {
    console.error('小红书解析失败:', error);
    throw error;
  }
}

// 获取真实URL（优化版本）
// 生成随机指纹信息
function generateRandomFingerprint() {
  // 生成随机IP地址
  const ip = `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
  
  return {
    ip: ip,
    sessionId: Math.random().toString(36).substring(2, 15),
    deviceId: Math.random().toString(36).substring(2, 15)
  };
}

// 生成伪造的Cookie
function generateFakeCookies() {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 15);
  
  return [
    `a1=${randomId}`,
    `webId=${randomId}`,
    `gid=${randomId}`,
    `timestamp=${timestamp}`,
    `webBuild=4.23.1`,
    `xsecappid=xhs-pc-web`,
    `acw_tc=${randomId}`,
    `customerClientId=${randomId}`,
    `x-user-id-creator.xiaohongshu.com=${randomId}`
  ].join('; ');
}

// 统一的请求headers配置 - PC端伪造
const COMMON_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'Accept-Encoding': 'gzip, deflate, br',
  'Cache-Control': 'no-cache',
  'Pragma': 'no-cache',
  'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
  'Sec-Ch-Ua-Mobile': '?0',
  'Sec-Ch-Ua-Platform': '"Windows"',
  'Sec-Fetch-Dest': 'document',
  'Sec-Fetch-Mode': 'navigate',
  'Sec-Fetch-Site': 'none',
  'Sec-Fetch-User': '?1',
  'Upgrade-Insecure-Requests': '1',
  'Connection': 'keep-alive'
};

// 获取真实URL（处理重定向）
async function getRealUrl(shareUrl) {
  try {
    const randomFingerprint = generateRandomFingerprint();
    
    const response = await uniCloud.httpclient.request(shareUrl, {
      method: 'GET',
      followRedirect: false,
      timeout: 15000,
      headers: {
        ...COMMON_HEADERS,
        'X-Forwarded-For': randomFingerprint.ip,
        'X-Real-IP': randomFingerprint.ip,
        'Cookie': generateFakeCookies()
      }
    });

    const location = response.headers.location || response.headers.Location;
    const realUrl = location || shareUrl;
    
    console.log('使用PC端请求获取真实URL:', realUrl);
    
    return realUrl;
  } catch (error) {
    console.error('获取真实URL失败:', error);
    return shareUrl;
  }
}

// 从URL中提取内容类型
function extractContentType(url) {
  try {
    const typeMatch = url.match(/[?&]type=([^&]+)/i);
    if (typeMatch) {
      const type = typeMatch[1].toLowerCase();
      if (type === 'video') return 'video';
      if (type === 'normal' || type === 'image') return 'image';
    }
    return 'unknown';
  } catch (error) {
    return 'unknown';
  }
}


// 提取页面基本信息（标题、作者和正文）
async function extractBasicInfo(html) {
  try {
    let title = '';
    let author = '';
    let content = '';

    // 从JSON数据中提取
    const initialStateMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({.+?})\s*<\/script>/s);
    if (initialStateMatch) {
      try {
        let jsonString = initialStateMatch[1];
        // 处理undefined
        jsonString = jsonString.replace(/undefined/g, 'null');
        const jsonData = JSON.parse(jsonString);
        
        let noteData = null;
        
        //note -> noteDetailMap -> firstNoteId -> note
        const noteRoot = jsonData?.note;
        if (noteRoot && noteRoot.firstNoteId && noteRoot.noteDetailMap) {
          const firstNoteId = noteRoot.firstNoteId;
          const noteDetailMap = noteRoot.noteDetailMap;
          if (noteDetailMap[firstNoteId] && noteDetailMap[firstNoteId].note) {
            noteData = noteDetailMap[firstNoteId].note;
          }
        }
        
        if (noteData) {
          // 标题：直接使用noteData.title，空就是空
          title = noteData.title || '';
          
          // 正文：直接使用noteData.desc，空就是空  
          content = noteData.desc || '';
          
          // 作者：从user.nickName或nickname获取
          author = noteData.user?.nickName || noteData.user?.nickname || '';
        }

      } catch (e) {
        console.error('JSON解析失败:', e.message);
      }
    }

    return {
      title: title,
      author: author, 
      content: content
    };
  } catch (error) {
    console.error('提取基本信息失败:', error);
    return {
      title: '',
      author: '',
      content: ''
    };
  }
}

// 获取页面内容
// 获取页面HTML内容
async function getPageContent(url) {
  try {
    console.log('获取JSON的URL:', url);
    
    // 生成随机的伪造信息
    const randomFingerprint = generateRandomFingerprint();
    
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      timeout: 15000, // 增加超时时间
      dataType: 'text',
      headers: {
        ...COMMON_HEADERS,
        'Referer': 'https://www.xiaohongshu.com/',
        'Origin': 'https://www.xiaohongshu.com',
        'X-Requested-With': 'XMLHttpRequest',
        'X-Forwarded-For': randomFingerprint.ip,
        'X-Real-IP': randomFingerprint.ip,
        'Cookie': generateFakeCookies()
      }
    });


    let htmlContent = response.data;
    if (typeof htmlContent !== 'string') {
      htmlContent = htmlContent?.toString() || '';
      if (!htmlContent) {
        throw new Error('页面内容格式错误');
      }
    }

    return htmlContent;
  } catch (error) {
    console.error('获取页面内容失败:', error);
    throw new Error('无法获取页面内容: ' + error.message);
  }
}
