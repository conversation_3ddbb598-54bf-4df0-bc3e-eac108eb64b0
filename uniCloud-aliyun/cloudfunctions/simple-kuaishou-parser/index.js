'use strict';

/**
 * 快手内容解析器
 * 支持快手视频和图文内容的解析，包括去水印处理
 */

exports.main = async (event, context) => {
  const { link, forceRemoveWatermark = false } = event;

  if (!link) {
    return {
      success: false,
      message: '链接不能为空'
    };
  }

  try {
    // 检测是否为快手链接
    if (!isKuaishouLink(link)) {
      return {
        success: false,
        message: '仅支持快手链接'
      };
    }

    // 解析快手内容
    const result = await parseKuaishouContent(link);

    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      version: "快手解析器"
    };

  } catch (error) {
    console.error('快手解析失败:', error);
    return {
      success: false,
      message: error.message || '解析失败',
      error: undefined
    };
  }
};

// 检测是否为快手链接
function isKuaishouLink(url) {
  const patterns = [
    /kuaishou\.com/i,
    /kwai\.app/i,
    /ks\.app/i,
    /gifshow\.com/i
  ];
  return patterns.some(pattern => pattern.test(url));
}

// 移动端请求头
const MOBILE_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
};

// 获取重定向后的真实URL (简化版本，模仿PHP)
async function getRealUrl(shareUrl) {
  try {
    console.log('获取快手真实URL:', shareUrl);
    
    const response = await uniCloud.httpclient.request(shareUrl, {
      method: 'GET',
      followRedirect: false,
      timeout: 15000,
      headers: MOBILE_HEADERS
    });

    const location = response.headers.location || response.headers.Location;
    const realUrl = location || shareUrl;
    
    console.log('真实URL:', realUrl);
    
    return realUrl;
  } catch (error) {
    console.error('获取真实URL失败:', error);
    return shareUrl;
  }
}

// 从URL中提取内容ID和类型
function extractContentInfo(url) {
  try {
    // 统一ID提取模式
    const patterns = [
      { regex: /short-video\/([^?&]+)/, type: 'video' },
      { regex: /photo\/([^?&]+)/, type: 'photo' },
      { regex: /photoId=([^&]+)/, type: 'photo' },
      { regex: /fw\/photo\/([^?&]+)/, type: 'photo' }
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern.regex);
      if (match) {
        const id = match[1];
        console.log(`✓ 提取到内容ID: ${id} (${pattern.type})`);
        return {
          id: id,
          type: pattern.type,
          requestUrl: `https://www.kuaishou.com/short-video/${id}`
        };
      }
    }

    // 处理短链接
    if (url.match(/v\.kuaishou\.com|v\.m\.chenzhongtech\.com|v\.m\.kuaishou\.com/)) {
      console.log('检测到短链接，需要重定向获取内容ID');
      return {
        id: 'pending',
        type: 'video',
        requestUrl: url
      };
    }
    
    console.log('⚠️ 未能从URL中提取内容ID');
    return {
      id: null,
      type: 'unknown',
      requestUrl: url
    };
  } catch (error) {
    console.error('提取内容信息失败:', error);
    return {
      id: null,
      type: 'unknown',
      requestUrl: url
    };
  }
}

// 获取页面HTML内容 (简化版本，模仿PHP的策略)
async function getPageContent(url) {
  try {
    console.log('获取快手页面内容:', url);
    
    // 模仿PHP版本的简单请求策略
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      followRedirect: true, // 允许自动跟随重定向，模仿PHP的CURLOPT_FOLLOWLOCATION
      timeout: 15000,
      dataType: 'text',
      headers: MOBILE_HEADERS
    });

    let htmlContent = response.data;
    if (typeof htmlContent !== 'string') {
      htmlContent = htmlContent?.toString() || '';
      if (!htmlContent) {
        throw new Error('页面内容格式错误');
      }
    }


    return htmlContent;
  } catch (error) {
    console.error('获取页面内容失败:', error);
    throw new Error('无法获取页面内容: ' + error.message);
  }
}

// 解析快手内容 (模仿PHP版本策略)
async function parseKuaishouContent(shareUrl) {
  try {
    console.log('开始解析快手内容:', shareUrl);
    
    // 获取真实URL和内容信息
    const realUrl = await getRealUrl(shareUrl);
    const contentInfo = extractContentInfo(realUrl);
    console.log('内容信息:', contentInfo);
    
    if (!contentInfo.id || contentInfo.id === null) {
      throw new Error('无法提取内容ID，URL: ' + realUrl);
    }
    
    // 如果是短链接，需要先从重定向URL中提取真实ID
    if (contentInfo.id === 'pending') {
      console.log('短链接需要从重定向URL中提取内容ID...');
      const redirectedInfo = extractContentInfo(realUrl);
      if (redirectedInfo.id && redirectedInfo.id !== 'pending') {
        contentInfo.id = redirectedInfo.id;
        contentInfo.type = redirectedInfo.type;
        console.log('从重定向URL提取到内容ID:', contentInfo.id, '类型:', contentInfo.type);
      } else {
        throw new Error('短链接重定向后仍无法提取内容ID，URL: ' + realUrl);
      }
    }

    // 构造带完整参数的最终URL
    const buildFinalUrl = (baseUrl, realUrl) => {
      try {
        const realUrlObj = new URL(realUrl);
        const params = realUrlObj.search;
        
        // 如果realUrl有参数，则添加到baseUrl
        if (params && baseUrl.indexOf('?') === -1) {
          return baseUrl + params;
        }
        return baseUrl;
      } catch (error) {
        return baseUrl;
      }
    };

    // 构建请求URL
    const requestUrls = [
      buildFinalUrl(`https://www.kuaishou.com/short-video/${contentInfo.id}`, realUrl),
    ];
    
    console.log('获取页面内容');

    let pageContent = null;
    let successUrl = null;
    let successStrategy = null;

    for (const url of requestUrls) {
      try {
        console.log(`  访问:`, url);
        
        // 请求内容
        const response = await uniCloud.httpclient.request(url, {
          method: 'GET',
          followRedirect: true,
          timeout: 15000,
          dataType: 'text',
          headers: {
            ...MOBILE_HEADERS,
            
          }
        });

        let htmlContent = response.data;
        if (typeof htmlContent !== 'string') {
          htmlContent = htmlContent?.toString() || '';
        }
        
        // 检查页面内容质量
        const hasServerError = htmlContent.includes('internal server error') &&
                               htmlContent.includes('"result":1000') && 
                               htmlContent.includes('error_msg');
        
        const isLongContent = htmlContent.length > 1000;
        const hasHTML = htmlContent.includes('<html') || htmlContent.includes('<!DOCTYPE');
        const hasInitState = htmlContent.includes('window.INIT_STATE');
        
        // 检查内容是否有效
        const isValidContent = htmlContent && !hasServerError && 
                              (isLongContent || hasHTML || hasInitState);
        
        if (isValidContent) {
          console.log(`  ✅ 成功获取有效内容! 内容长度: ${htmlContent.length}`);
          pageContent = htmlContent;
          successUrl = url;
          successStrategy = url.includes('/short-video/') ? 'short-video' : 'photo';
          break;
        } else {
          console.log(`  ❌ 内容无效，长度: ${htmlContent.length}`);
        }
      } catch (error) {
        console.log(`  ❌ 请求失败:`, error.message);
        continue;
      }
    }
    
    // 检查是否获取到有效内容
    if (!pageContent || !successUrl) {
      throw new Error('无法获取到有效页面内容');
    }

    // 提取JSON数据
    console.log('开始提取页面数据...');
    
    let extractedData = null;
    let dataSource = null;
    
    // 提取window.INIT_STATE
    if (pageContent.includes('window.INIT_STATE')) {
      console.log('✓ 找到window.INIT_STATE');
      try {
        const initStateMatch = pageContent.match(/window\.INIT_STATE\s*=\s*({.+?})\s*<\/script>/s);
        if (initStateMatch) {
          const jsonStr = initStateMatch[1];
          extractedData = JSON.parse(jsonStr);
          dataSource = 'window.INIT_STATE';
          console.log('✓ 成功解析JSON数据');
        }
      } catch (error) {
        console.log('❌ JSON解析失败:', error.message);
      }
    }
    
    // 简单日志输出
    if (extractedData) {
      console.log(`✅ 成功提取JSON数据`);
      console.log(`数据源: ${dataSource}`);
      console.log(`JSON大小: ${JSON.stringify(extractedData).length} 字符`);
    } else {
      console.log('❌ 未找到任何可提取的JSON数据');
    }
    
    // 解析快手视频数据
    let parsedVideoData = null;
    if (extractedData) {
      parsedVideoData = parseKuaishouVideoData(extractedData);
      console.log('解析的视频数据:', parsedVideoData);
    }
    
    // 返回解析结果
    if (parsedVideoData) {
      return {
        title: parsedVideoData.title,
        author: parsedVideoData.author,
        content: parsedVideoData.content,
        videoUrl: parsedVideoData.videoUrl,
        coverUrl: parsedVideoData.coverUrl,
        userAvatar: parsedVideoData.userAvatar,
        duration: parsedVideoData.duration,
        viewCount: parsedVideoData.viewCount,
        likeCount: parsedVideoData.likeCount,
        commentCount: parsedVideoData.commentCount,
        type: parsedVideoData.contentType?.type === 'image_text' ? 'image' : parsedVideoData.contentType?.type === 'single_image' ? 'image' : 'video',
        platform: 'kuaishou',
        source: '快手',
        originalUrl: shareUrl,
        processedData: {
          // 根据内容类型设置正确的data字段
          data: parsedVideoData.contentType?.type === 'video' 
            ? parsedVideoData.videoUrl // 视频内容：直接使用视频URL
            : (parsedVideoData.contentType?.type === 'image_text' || parsedVideoData.contentType?.type === 'single_image')
              ? parsedVideoData.imageUrls?.[0]?.url || '' // 图片内容：使用第一张图片URL
              : extractedData, // 其他情况：使用原始数据
          isUrl: true, // 标记为URL格式，前端据此处理
          dataSource: dataSource,
          parsedData: parsedVideoData,
          type: 'video_parsed',
          htmlLength: pageContent.length,
          jsonSize: extractedData ? JSON.stringify(extractedData).length : 0,
          // 添加前端需要的字段
          imageUrls: parsedVideoData.contentType?.type === 'image_text' || parsedVideoData.contentType?.type === 'single_image' 
            ? parsedVideoData.imageUrls?.map(img => img.url) || []
            : [],
          videoUrls: parsedVideoData.videoUrls?.map(video => video.url) || []
        },
        debug: {
          realUrl: realUrl,
          contentType: contentInfo.type,
          contentId: contentInfo.id,
          requestUrl: successUrl,
          dataSource: dataSource,
          extractSuccess: !!extractedData,
          parseSuccess: !!parsedVideoData
        }
      };
    } else {
      return {
        title: extractedData ? 'JSON提取成功但解析失败' : 'JSON提取失败',
        author: '数据提取',
        content: extractedData ? `成功从${dataSource}提取JSON数据，但未能解析视频信息` : '未找到有效的JSON数据',
        processedData: {
          data: extractedData,
          dataSource: dataSource,
          type: 'json_extracted',
          isUrl: false,
          htmlLength: pageContent.length,
          jsonSize: extractedData ? JSON.stringify(extractedData).length : 0
        },
        type: 'json_extracted',
        platform: 'kuaishou',
        source: '快手',
        coverUrl: null,
        originalUrl: shareUrl,
        debug: {
          realUrl: realUrl,
          contentType: contentInfo.type,
          contentId: contentInfo.id,
          requestUrl: successUrl,
          successStrategy: successStrategy,
          dataSource: dataSource,
          extractSuccess: !!extractedData,
          parseSuccess: false
        }
      };
    }

  } catch (error) {
    console.error('快手解析失败:', error);
    throw error;
  }
}

// 解析快手视频数据
function parseKuaishouVideoData(initStateData) {
  try {
    console.log('开始解析快手视频数据...');
    
    // 递归搜索函数
    function findVideoData(obj, depth = 0) {
      if (depth > 3) return null; // 限制搜索深度防止无限递归
      
      for (const key in obj) {
        const item = obj[key];
        
        // 检查当前对象是否包含视频数据
        if (item && typeof item === 'object' && 
            item.caption && item.userName && item.mainMvUrls && item.coverUrls) {
          console.log(`找到视频数据，深度: ${depth}, 路径键: ${key.substring(0, 50)}...`);
          return item;
        }
        
        // 递归搜索子对象
        if (item && typeof item === 'object') {
          const result = findVideoData(item, depth + 1);
          if (result) return result;
        }
      }
      return null;
    }
    
    // 递归查找所有质量版本的视频URL
    function findAllVideoUrls(obj, depth = 0) {
      const videoUrls = [];
      if (depth > 6) return videoUrls; // 限制递归深度
      
      for (const key in obj) {
        const item = obj[key];
        if (item && typeof item === 'object') {
          // 检查是否是视频URL对象
          if (item.url && typeof item.url === 'string' && item.url.includes('.mp4')) {
            const quality = getVideoQualityFromUrl(item.url);
            const cdn = extractCdnFromUrl(item.url);
            videoUrls.push({
              url: item.url,
              cdn: cdn,
              quality: quality,
              source: 'direct'
            });
            console.log(`发现视频URL: ${quality} (${cdn})`);
          }
          
          // 继续递归查找
          const subUrls = findAllVideoUrls(item, depth + 1);
          videoUrls.push(...subUrls);
        }
      }
      return videoUrls;
    }
    
    // 开始递归搜索
    const videoItem = findVideoData(initStateData);
    
    if (videoItem) {
        
        console.log('找到视频数据，开始解析...');
        
        // 提取基本信息
        const title = videoItem.caption || '';
        const author = videoItem.userName || '';
        const duration = videoItem.duration || 0;
        const photoType = videoItem.photoType || '';
        
        // 检测内容类型
        const contentType = getKuaishouContentType(videoItem);
        console.log(`📋 内容类型: ${contentType.description} (${contentType.type})`);
        
        // 根据内容类型提取对应的媒体资源
        let videoUrls = [];
        let imageUrls = [];
        
        if (contentType.type === 'video') {
            // 纯视频：查找所有质量的视频URL
            console.log('开始查找所有质量的视频URL...');
            let allVideoUrls = findAllVideoUrls(initStateData);
            
            // 从mainMvUrls中添加标准质量视频URL
            if (videoItem.mainMvUrls && videoItem.mainMvUrls.length > 0) {
              const mainUrls = videoItem.mainMvUrls.map(mv => ({
                url: mv.url || '',
                cdn: mv.cdn || '',
                quality: getVideoQualityFromUrl(mv.url || ''),
                source: 'mainMvUrls'
              }));
              videoUrls.push(...mainUrls);
            }
            
            // 合并所有视频URL并去重
            const allUrls = [...videoUrls, ...allVideoUrls];
            const uniqueUrls = [];
            const seenUrls = new Set();
            
            for (const urlInfo of allUrls) {
              const urlKey = urlInfo.url.split('?')[0]; // 去除参数部分进行去重
              if (!seenUrls.has(urlKey)) {
                seenUrls.add(urlKey);
                uniqueUrls.push(urlInfo);
              }
            }
            
            // 按质量排序：UltraV5 > HighV5 > Standard
            uniqueUrls.sort((a, b) => {
              const qualityOrder = { 
                'UltraV5 (4K)': 4, 
                'UltraV5': 3, 
                'HighV5 (1080p)': 2, 
                'HighV5': 2, 
                'Standard (720p)': 1, 
                'Standard': 1 
              };
              const aScore = qualityOrder[a.quality] || 0;
              const bScore = qualityOrder[b.quality] || 0;
              return bScore - aScore;
            });
            
            videoUrls = uniqueUrls;
            console.log(`找到 ${videoUrls.length} 个视频URL，质量分布:`, 
                       videoUrls.map(v => `${v.quality}(${v.cdn})`));
                       
        } else if (contentType.type === 'single_image' || contentType.type === 'image_text') {
            // 单张图片或图集：提取图片URL
            console.log('开始提取图片URL...');
            imageUrls = extractImageUrls(videoItem, initStateData);
            

            
            // 去重图片URL
            const uniqueImageUrls = [];
            const seenImageUrls = new Set();
            
            for (const imgInfo of imageUrls) {
              const urlKey = imgInfo.url.split('?')[0];
              if (!seenImageUrls.has(urlKey)) {
                seenImageUrls.add(urlKey);
                uniqueImageUrls.push(imgInfo);
              }
            }
            
            imageUrls = uniqueImageUrls;
            console.log(`找到 ${imageUrls.length} 张图片，格式分布:`, 
                       imageUrls.map(img => `${img.format}(${img.cdn})`));
        }
        
        // 提取封面图URL（收集所有CDN）
        let coverUrl = '';
        let coverUrls = [];
        if (videoItem.coverUrls && videoItem.coverUrls.length > 0) {
          coverUrls = videoItem.coverUrls.map(cover => ({
            url: cover.url || '',
            cdn: cover.cdn || ''
          }));
          coverUrl = coverUrls[0]?.url || '';
        }
        
        // 提取用户头像URL（收集所有CDN）
        let userAvatar = '';
        let headUrls = [];
        if (videoItem.headUrls && videoItem.headUrls.length > 0) {
          headUrls = videoItem.headUrls.map(head => ({
            url: head.url || '',
            cdn: head.cdn || ''
          }));
          userAvatar = headUrls[0]?.url || '';
        }
        
        // 提取统计数据
        const viewCount = videoItem.viewCount || 0;
        const likeCount = videoItem.likeCount || 0;
        const commentCount = videoItem.commentCount || 0;
        const shareCount = videoItem.shareCount || 0;
        
        // 保持原始标题，不进行清理
        const originalTitle = title;
        
        // 提取标签
        const tags = [];
        const tagMatches = title.match(/#[^\s#]*/g);
        if (tagMatches) {
          tags.push(...tagMatches.map(tag => tag.replace('#', '')));
        }
        
        // 简化输出
        console.log(`✅ ${contentType.description}解析成功: ${originalTitle}`);
        console.log(`作者: ${author} | ${contentType.type === 'video' ? `视频${videoUrls.length}个` : `图片${imageUrls.length}张`}`);
        
        // 根据内容类型设置主要URL
        const videoUrl = videoUrls.length > 0 ? videoUrls[0].url : '';
        const imageUrl = imageUrls.length > 0 ? imageUrls[0].url : '';
        
        return {
          title: originalTitle, // 保持原始标题（包含标签）
          content: originalTitle, // 保留原始内容
          author: author,
          videoUrl: videoUrl,
          imageUrl: imageUrl, // 新增图片URL字段
          coverUrl: coverUrl,
          userAvatar: userAvatar,
          duration: Math.floor(duration / 1000), // 转换为秒
          viewCount: viewCount,
          likeCount: likeCount,
          commentCount: commentCount,
          shareCount: shareCount,
          tags: tags,
          videoUrls: videoUrls, // 包含所有质量版本
          imageUrls: imageUrls, // 新增图片URL数组
          contentType: contentType, // 新增内容类型信息
          userId: videoItem.userId || '',
          timestamp: videoItem.timestamp || 0,
          videoId: videoItem.videoId || ''
        };
    }
    
    console.log('未找到匹配的视频数据结构');
    return null;
    
  } catch (error) {
    console.error('解析快手视频数据失败:', error);
    return null;
  }
}


// 从URL中提取CDN信息
function extractCdnFromUrl(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    return 'unknown';
  }
}

// 改进的视频质量检测函数，支持更多URL格式
function getVideoQualityFromUrl(url) {
  if (url.includes('_v6UltraV5.mp4') || url.includes('tt=v6UltraV5')) {
    return 'UltraV5 (4K)';
  } else if (url.includes('_v6HighV5.mp4') || url.includes('tt=v6HighV5')) {
    return 'HighV5 (1080p)';
  } else if (url.includes('tt=b') || url.includes('_b_B')) {
    return 'Standard (720p)';
  } else {
    return 'Standard (720p)';
  }
}

// 检测快手内容类型
function getKuaishouContentType(videoItem) {
  switch (videoItem.photoType) {
    case "VIDEO":
      return {
        type: "video",
        description: "纯视频",
        hasVideo: true,
        hasImages: false
      };
      
    case "HORIZONTAL_ATLAS":
      return {
        type: "image_text", 
        description: "图文视频/图集",
        hasVideo: false,
        hasImages: true
      };
      
    case "SINGLE_PICTURE":
      return {
        type: "single_image",
        description: "单张图片",
        hasVideo: false, 
        hasImages: true
      };
      
    default:
      return {
        type: "unknown",
        description: "未知类型",
        photoType: videoItem.photoType || "undefined"
      };
  }
}

// 提取图片URL (支持单张图片和图集)
function extractImageUrls(videoItem, initStateData) {
  const imageUrls = [];
  const contentType = getKuaishouContentType(videoItem);
  
  if (contentType.type === 'single_image') {
    // 单张图片：从coverUrls或其他图片字段提取
    if (videoItem.coverUrls && videoItem.coverUrls.length > 0) {
      videoItem.coverUrls.forEach((cover, index) => {
        imageUrls.push({
          url: cover.url || '',
          cdn: cover.cdn || '',
          index: index,
          format: getImageFormat(cover.url || ''),
          type: 'cover'
        });
      });
    }
    
    // 也检查是否有atlas结构（有些单图也可能在atlas中）
    if (videoItem.ext_params && videoItem.ext_params.atlas && videoItem.ext_params.atlas.list) {
      videoItem.ext_params.atlas.list.forEach((imagePath, index) => {
        const cdnList = videoItem.ext_params.atlas.cdnList || [];
        cdnList.forEach(cdnInfo => {
          const fullUrl = `https://${cdnInfo.cdn}${imagePath}`;
          imageUrls.push({
            url: fullUrl,
            cdn: cdnInfo.cdn,
            index: index,
            format: getImageFormat(imagePath),
            type: 'atlas'
          });
        });
      });
    }
    
  } else if (contentType.type === 'image_text') {
    // 图文视频/图集：从atlas结构提取所有图片
    

    
    // 从ext_params.atlas提取（WebP格式）
    if (videoItem.ext_params && videoItem.ext_params.atlas && videoItem.ext_params.atlas.list) {
      const cdnList = videoItem.ext_params.atlas.cdnList || [];
      videoItem.ext_params.atlas.list.forEach((imagePath, index) => {
        cdnList.forEach(cdnInfo => {
          const fullUrl = `https://${cdnInfo.cdn}${imagePath}`;
          imageUrls.push({
            url: fullUrl,
            cdn: cdnInfo.cdn,
            index: index,
            format: getImageFormat(imagePath),
            type: 'atlas_webp'
          });
        });
      });
    }
    
    // 从顶层atlas提取（JPG格式）
    const atlasData = findAtlasInData(initStateData);
    if (atlasData && atlasData.list) {
      const cdnList = atlasData.cdnList || atlasData.cdn || [];
      atlasData.list.forEach((imagePath, index) => {
        if (Array.isArray(cdnList)) {
          cdnList.forEach(cdnItem => {
            // 处理CDN可能是字符串或对象的情况
            const cdnName = typeof cdnItem === 'string' ? cdnItem : (cdnItem.cdn || cdnItem.name || 'unknown');
            const fullUrl = `https://${cdnName}${imagePath}`;
            imageUrls.push({
              url: fullUrl,
              cdn: cdnName,
              index: index,
              format: getImageFormat(imagePath),
              type: 'atlas_jpg'
            });
          });
        }
      });
    }
  }
  
  // 对图片进行去重和优选，每张图片只保留最佳版本
  const optimizedImageUrls = optimizeImageUrls(imageUrls);
  
  return optimizedImageUrls;
}

// 优化图片URL数组，去重并为每张图片选择最佳版本
function optimizeImageUrls(imageUrls) {
  const groupedImages = {};
  imageUrls.forEach(img => {
    const index = img.index;
    if (!groupedImages[index]) {
      groupedImages[index] = [];
    }
    groupedImages[index].push(img);
  });
  
  const optimizedUrls = [];
  
  // 为每组图片选择最佳版本（WEBP格式优先）
  Object.keys(groupedImages).sort((a, b) => parseInt(a) - parseInt(b)).forEach(index => {
    const images = groupedImages[index];
    
    const bestImage = images.reduce((best, current) => {
      const formatPriority = { 'webp': 3, 'jpg': 2, 'png': 1, 'unknown': 0 };
      const bestFormatScore = formatPriority[best.format] || 0;
      const currentFormatScore = formatPriority[current.format] || 0;
      
      if (currentFormatScore > bestFormatScore) {
        return current;
      } else if (currentFormatScore < bestFormatScore) {
        return best;
      }
      
      // 格式相同时优选CDN
      const preferredCdns = ['p3.a.yximgs.com', 'p4.a.yximgs.com'];
      const bestCdnPriority = preferredCdns.includes(best.cdn) ? 1 : 0;
      const currentCdnPriority = preferredCdns.includes(current.cdn) ? 1 : 0;
      
      return currentCdnPriority >= bestCdnPriority ? current : best;
    });
    
    optimizedUrls.push(bestImage);
  });
  
  return optimizedUrls;
}

// 获取图片格式
function getImageFormat(url) {
  if (url.includes('.webp')) return 'webp';
  if (url.includes('.jpg') || url.includes('.jpeg')) return 'jpg';
  if (url.includes('.png')) return 'png';
  return 'unknown';
}

// 在数据中查找atlas结构
function findAtlasInData(data, depth = 0) {
  if (depth > 3) return null;
  
  for (const key in data) {
    const item = data[key];
    if (item && typeof item === 'object') {
      if (key === 'atlas' && item.list && Array.isArray(item.list)) {
        return item;
      }
      
      const result = findAtlasInData(item, depth + 1);
      if (result) return result;
    }
  }
  return null;
}

